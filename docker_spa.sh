#!/bin/bash

# 清除现有规则
tc qdisc del dev eno2 ingress 2>/dev/null
tc qdisc del dev enp3s0f0 ingress 2>/dev/null

# 添加 ingress qdisc
tc qdisc add dev eno2 ingress handle ffff:
tc qdisc add dev enp3s0f0 ingress handle ffff:

# 定义网络和对应的 IP 列表
declare -A networks
networks=(
    ["my_network_1"]="************** ************** ************** ************** ************** ************** ************** ************** ************** ************** ************** ************** ************** ************** ************** ************** ************** ************** ************** **************"
    ["my_network_2"]="*************** *************** *************** *************** *************** *************** *************** *************** *************** *************** *************** *************** *************** *************** *************** *************** *************** *************** *************** ***************"
)


# 镜像名称
IMAGE="debian:latest"
# 启动命令
COMMAND="/soft/start.sh"

# 全局计数器
global_index=0

# 循环遍历每个网络
for network in "${!networks[@]}"; do
    IPS=(${networks[$network]})
    for i in "${!IPS[@]}"; do
        global_index=$((global_index + 1))
        prio=$global_index
        flowid="ffff:$global_index"
        ip=${IPS[$i]}
        tc filter add dev eno1 parent ffff: protocol ip prio $prio u32 match ip dst $ip/32 flowid $flowid action police rate 200mbit burst 1m conform-exceed drop

        # 计算容器名称（t1, t2, ...）
        container_name="t$global_index"
        ip=${IPS[$i]}

        # 检查容器是否已存在
        if docker ps -a -f name=^${container_name}$ | grep -q ${container_name}; then
            echo "容器 ${container_name} 已存在，跳过创建"
        else
            echo "创建容器 ${container_name}，IP: ${ip}"
            docker run -d -t --name ${container_name} \
                --network ${network} --ip ${ip} \
                --privileged \
                --entrypoint /bin/sh \
                -e TAG=${container_name} \
                -v /home/<USER>/soft:/soft \
                ${IMAGE} 
            if [ $? -eq 0 ]; then
                echo "容器 ${container_name} 创建成功"
                sleep 2
                docker exec ${container_name} ${COMMAND}
            else
                echo "容器 ${container_name} 创建失败"
            fi
        fi
    done
done